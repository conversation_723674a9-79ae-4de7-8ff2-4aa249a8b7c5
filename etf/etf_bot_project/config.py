"""
ETF机器人配置文件
包含API密钥、重试参数等配置信息
支持多个AI模型提供商：OpenAI、千问、火山云
"""
import os
from typing import Dict, Any

class Config:
    """配置管理类"""

    def __init__(self):
        # AI模型配置
        self.AI_PROVIDER = os.getenv('AI_PROVIDER', 'openai')  # openai, qwen, volcengine

        # OpenAI配置
        self.OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '************************************************************')
        self.OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')

        # 千问配置
        self.QWEN_API_KEY = os.getenv('QWEN_API_KEY', 'sk-7a4b7b688222486087c38a31f3921c6f')  # 请设置有效的千问API密钥
        self.QWEN_MODEL = os.getenv('QWEN_MODEL', 'qwen3-235b-a22b')  # 默认使用qwen-turbo模型

        # 火山云配置
        self.VOLCENGINE_API_KEY = os.getenv('VOLCENGINE_API_KEY', '94b27c38-0282cb8e1')
        self.VOLCENGINE_ENDPOINT = os.getenv('VOLCENGINE_ENDPOINT', 'ep-202t69')
        self.VOLCENGINE_MODEL = os.getenv('VOLCENGINE_MODEL', 'doubao-seed-1-6-250615')

        # 其他配置
        self.SERVER_CHAN_KEY = os.getenv('SERVER_CHAN_KEY', 'SCT289350TUnxoh')

        # 数据获取配置
        self.MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))
        self.RETRY_DELAY = float(os.getenv('RETRY_DELAY', '1.0'))
        self.CACHE_TIMEOUT = int(os.getenv('CACHE_TIMEOUT', '300'))  # 5分钟
        self.HISTORICAL_CACHE_TIMEOUT = int(os.getenv('HISTORICAL_CACHE_TIMEOUT', '3600'))  # 1小时

        # ETF分析配置
        self.LOOKBACK_DAYS = int(os.getenv('LOOKBACK_DAYS', '365'))
        self.RETREAT_THRESHOLDS = [-5, -10, -15]
        self.RISE_THRESHOLDS = [5, 10, 15]

        # AI模型通用配置
        self.AI_TEMPERATURE = float(os.getenv('AI_TEMPERATURE', '0.3'))
        self.AI_MAX_RETRIES = int(os.getenv('AI_MAX_RETRIES', '3'))
        self.AI_MAX_TOKENS = int(os.getenv('AI_MAX_TOKENS', '4000'))

        # 向后兼容的GPT配置
        self.GPT_MODEL = os.getenv('GPT_MODEL', 'gpt-4o-mini')
        self.GPT_TEMPERATURE = self.AI_TEMPERATURE
        self.GPT_MAX_RETRIES = self.AI_MAX_RETRIES
        
        # AI模型配置映射
        self.AI_MODELS = {
            'openai': {
                'models': ['gpt-4o-mini', 'gpt-4o', 'gpt-3.5-turbo'],
                'default_model': 'gpt-4o-mini',
                'api_key_env': 'OPENAI_API_KEY',
                'display_name': 'OpenAI GPT'
            },
            'qwen': {
                'models': ['qwen-turbo', 'qwen-plus', 'qwen-max'],
                'default_model': 'qwen-turbo',
                'api_key_env': 'QWEN_API_KEY',
                'display_name': '千问 (Qwen)'
            },
            'volcengine': {
                'models': ['doubao-lite-4k', 'doubao-pro-4k', 'doubao-pro-32k'],
                'default_model': 'doubao-lite-4k',
                'api_key_env': 'VOLCENGINE_API_KEY',
                'display_name': '火山云 (豆包)'
            }
        }

        # 文件路径配置
        self.CONFIG_FILE = "etf_bot_project/etf_data_config.py"
        self.PRICE_HISTORY_FILE = "etf_price_history.csv"

        # 数据源配置
        self.DATA_SOURCES = {
            'akshare': {
                'enabled': True,
                'priority': 1,
                'timeout': 30
            }
            # 可以在这里添加更多数据源
        }

        # 交易所映射
        self.EXCHANGE_MAPPING = {
            'SH_PREFIXES': ['50', '51', '52', '56', '58'],
            'SZ_PREFIXES': ['15', '16', '17', '18', '19']
        }

        # 价格验证配置
        self.PRICE_VALIDATION = {
            'min_price': 0.1,
            'max_price': 1000.0,
            'required_columns': ['收盘', '开盘', '最高', '最低']
        }
    
    def get_exchange_suffix(self, symbol: str) -> str:
        """根据ETF代码确定交易所后缀"""
        for prefix in self.EXCHANGE_MAPPING['SH_PREFIXES']:
            if symbol.startswith(prefix):
                return '.SH'
        
        for prefix in self.EXCHANGE_MAPPING['SZ_PREFIXES']:
            if symbol.startswith(prefix):
                return '.SZ'
        
        # 默认返回上海交易所
        return '.SH'
    
    def get_current_model_config(self) -> Dict[str, Any]:
        """获取当前AI提供商的配置"""
        if self.AI_PROVIDER in self.AI_MODELS:
            return self.AI_MODELS[self.AI_PROVIDER]
        return self.AI_MODELS['openai']  # 默认返回OpenAI配置

    def get_available_providers(self) -> Dict[str, str]:
        """获取可用的AI提供商列表"""
        available = {}
        for provider, config in self.AI_MODELS.items():
            api_key_env = config['api_key_env']
            api_key = os.getenv(api_key_env) or getattr(self, api_key_env.replace('_API_KEY', '_API_KEY').lower(), '')
            if api_key:
                available[provider] = config['display_name']
        return available

    def set_ai_provider(self, provider: str) -> bool:
        """设置AI提供商"""
        if provider in self.AI_MODELS:
            self.AI_PROVIDER = provider
            return True
        return False

    def set_model_for_provider(self, provider: str, model: str) -> bool:
        """为指定提供商设置模型"""
        if provider in self.AI_MODELS:
            if model in self.AI_MODELS[provider]['models']:
                if provider == 'openai':
                    self.GPT_MODEL = model
                elif provider == 'qwen':
                    self.QWEN_MODEL = model
                elif provider == 'volcengine':
                    self.VOLCENGINE_MODEL = model
                return True
        return False

    def get_current_model(self) -> str:
        """获取当前提供商的当前模型"""
        if self.AI_PROVIDER == 'openai':
            return self.GPT_MODEL
        elif self.AI_PROVIDER == 'qwen':
            return self.QWEN_MODEL
        elif self.AI_PROVIDER == 'volcengine':
            return self.VOLCENGINE_MODEL
        return "unknown"

    def validate_config(self) -> bool:
        """验证配置的有效性"""
        # 检查基本配置
        if not self.SERVER_CHAN_KEY:
            print("警告: SERVER_CHAN_KEY 未配置")
            return False

        # 检查当前AI提供商的配置
        current_config = self.get_current_model_config()
        api_key_env = current_config['api_key_env']
        api_key = os.getenv(api_key_env) or getattr(self, api_key_env.replace('_API_KEY', '_API_KEY').lower(), '')

        if not api_key:
            print(f"警告: 当前AI提供商 {self.AI_PROVIDER} 的API密钥未配置")
            return False

        return True

# 全局配置实例
config = Config()

# 向后兼容的配置变量
OPENAI_API_KEY = config.OPENAI_API_KEY
SERVER_CHAN_KEY = config.SERVER_CHAN_KEY
LOOKBACK_DAYS = config.LOOKBACK_DAYS
CONFIG_FILE = config.CONFIG_FILE
